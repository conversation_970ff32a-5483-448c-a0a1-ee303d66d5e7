# PowerShell script to install system integration for Gemini CLI
# Run as Administrator for full system integration

param(
    [switch]$Uninstall
)

$ErrorActionPreference = "Stop"

# Get current script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$GeminiMT5Dir = $ScriptDir

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "  🔧 Gemini CLI System Integration     " -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if ($Uninstall) {
    Write-Host "🗑️ Uninstalling system integration..." -ForegroundColor Red
    
    # Remove Start Menu shortcuts
    $StartMenuPath = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\Gemini CLI"
    if (Test-Path $StartMenuPath) {
        Remove-Item -Path $StartMenuPath -Recurse -Force
        Write-Host "✅ Removed Start Menu shortcuts" -ForegroundColor Green
    }
    
    # Remove context menu entries
    $ContextMenuKeys = @(
        "HKCU:\Software\Classes\Directory\shell\GeminiCLI",
        "HKCU:\Software\Classes\Directory\Background\shell\GeminiCLI"
    )
    
    foreach ($key in $ContextMenuKeys) {
        if (Test-Path $key) {
            Remove-Item -Path $key -Recurse -Force
            Write-Host "✅ Removed context menu entry: $key" -ForegroundColor Green
        }
    }
    
    Write-Host "🎉 System integration uninstalled successfully!" -ForegroundColor Green
    return
}

Write-Host "🚀 Installing Gemini CLI system integration..." -ForegroundColor Green
Write-Host "📁 Installation directory: $GeminiMT5Dir" -ForegroundColor Yellow
Write-Host ""

# 1. Create Start Menu shortcuts
Write-Host "📋 Creating Start Menu shortcuts..." -ForegroundColor Cyan

$StartMenuPath = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\Gemini CLI"
if (-not (Test-Path $StartMenuPath)) {
    New-Item -ItemType Directory -Path $StartMenuPath -Force | Out-Null
}

# Create shortcuts
$WshShell = New-Object -comObject WScript.Shell

# Interactive Gemini CLI shortcut
$Shortcut1 = $WshShell.CreateShortcut("$StartMenuPath\Gemini CLI - Interactive.lnk")
$Shortcut1.TargetPath = "powershell.exe"
$Shortcut1.Arguments = "-NoExit -ExecutionPolicy Bypass -File `"$GeminiMT5Dir\gemini-mt5-advanced.ps1`""
$Shortcut1.WorkingDirectory = $GeminiMT5Dir
$Shortcut1.Description = "Gemini CLI Interactive Mode for MT5 Development"
$Shortcut1.IconLocation = "powershell.exe,0"
$Shortcut1.Save()

# Web Interface shortcut
$Shortcut2 = $WshShell.CreateShortcut("$StartMenuPath\Gemini CLI - Web Interface.lnk")
$Shortcut2.TargetPath = "$GeminiMT5Dir\start-web-interface.bat"
$Shortcut2.WorkingDirectory = $GeminiMT5Dir
$Shortcut2.Description = "Gemini CLI Web Interface for MT5 Development"
$Shortcut2.IconLocation = "shell32.dll,13"
$Shortcut2.Save()

# Templates shortcut
$Shortcut3 = $WshShell.CreateShortcut("$StartMenuPath\Gemini CLI - Templates.lnk")
$Shortcut3.TargetPath = "powershell.exe"
$Shortcut3.Arguments = "-NoExit -ExecutionPolicy Bypass -File `"$GeminiMT5Dir\gemini-mt5-advanced.ps1`" -Templates"
$Shortcut3.WorkingDirectory = $GeminiMT5Dir
$Shortcut3.Description = "View Gemini CLI MT5 Development Templates"
$Shortcut3.IconLocation = "shell32.dll,70"
$Shortcut3.Save()

Write-Host "✅ Start Menu shortcuts created" -ForegroundColor Green

# 2. Create context menu integration
Write-Host "🖱️ Creating context menu integration..." -ForegroundColor Cyan

# Right-click on folder
$FolderContextKey = "HKCU:\Software\Classes\Directory\shell\GeminiCLI"
New-Item -Path $FolderContextKey -Force | Out-Null
Set-ItemProperty -Path $FolderContextKey -Name "(Default)" -Value "🚀 Open Gemini CLI here"
Set-ItemProperty -Path $FolderContextKey -Name "Icon" -Value "powershell.exe,0"

$FolderCommandKey = "$FolderContextKey\command"
New-Item -Path $FolderCommandKey -Force | Out-Null
Set-ItemProperty -Path $FolderCommandKey -Name "(Default)" -Value "powershell.exe -NoExit -ExecutionPolicy Bypass -Command `"cd '%1'; & '$GeminiMT5Dir\gemini-mt5-advanced.ps1'`""

# Right-click on empty space in folder
$BackgroundContextKey = "HKCU:\Software\Classes\Directory\Background\shell\GeminiCLI"
New-Item -Path $BackgroundContextKey -Force | Out-Null
Set-ItemProperty -Path $BackgroundContextKey -Name "(Default)" -Value "🚀 Open Gemini CLI here"
Set-ItemProperty -Path $BackgroundContextKey -Name "Icon" -Value "powershell.exe,0"

$BackgroundCommandKey = "$BackgroundContextKey\command"
New-Item -Path $BackgroundCommandKey -Force | Out-Null
Set-ItemProperty -Path $BackgroundCommandKey -Name "(Default)" -Value "powershell.exe -NoExit -ExecutionPolicy Bypass -Command `"cd '%V'; & '$GeminiMT5Dir\gemini-mt5-advanced.ps1'`""

Write-Host "✅ Context menu integration created" -ForegroundColor Green

# 3. Create desktop shortcuts
Write-Host "🖥️ Creating desktop shortcuts..." -ForegroundColor Cyan

$DesktopPath = [Environment]::GetFolderPath("Desktop")

# Main desktop shortcut
$DesktopShortcut = $WshShell.CreateShortcut("$DesktopPath\Gemini CLI - MT5 Dev.lnk")
$DesktopShortcut.TargetPath = "powershell.exe"
$DesktopShortcut.Arguments = "-NoExit -ExecutionPolicy Bypass -File `"$GeminiMT5Dir\gemini-mt5-advanced.ps1`""
$DesktopShortcut.WorkingDirectory = $GeminiMT5Dir
$DesktopShortcut.Description = "Gemini CLI for MT5 Development"
$DesktopShortcut.IconLocation = "powershell.exe,0"
$DesktopShortcut.Save()

Write-Host "✅ Desktop shortcuts created" -ForegroundColor Green

# 4. Create quick launch script
Write-Host "⚡ Creating quick launch configuration..." -ForegroundColor Cyan

$QuickLaunchScript = @"
# Gemini CLI Quick Launch Configuration
# Add this to your PowerShell profile for instant access

function Start-GeminiCLI {
    param([string]`$Path = (Get-Location).Path)
    Set-Location `$Path
    & "$GeminiMT5Dir\gemini-mt5-advanced.ps1"
}

function Start-GeminiWeb {
    & "$GeminiMT5Dir\start-web-interface.bat"
}

function Get-GeminiTemplates {
    & "$GeminiMT5Dir\gemini-mt5-advanced.ps1" -Templates
}

# Aliases for quick access
Set-Alias -Name gcli -Value Start-GeminiCLI
Set-Alias -Name gweb -Value Start-GeminiWeb
Set-Alias -Name gtpl -Value Get-GeminiTemplates

Write-Host "🚀 Gemini CLI commands loaded:" -ForegroundColor Green
Write-Host "  gcli  - Start Gemini CLI" -ForegroundColor Yellow
Write-Host "  gweb  - Start Web Interface" -ForegroundColor Yellow
Write-Host "  gtpl  - Show Templates" -ForegroundColor Yellow
"@

$QuickLaunchScript | Out-File -FilePath "$GeminiMT5Dir\gemini-profile.ps1" -Encoding UTF8

Write-Host "✅ Quick launch configuration created" -ForegroundColor Green

# 5. Summary
Write-Host ""
Write-Host "🎉 System integration installed successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 What's been installed:" -ForegroundColor Yellow
Write-Host "  ✅ Start Menu shortcuts (Gemini CLI folder)" -ForegroundColor White
Write-Host "  ✅ Desktop shortcut (Gemini CLI - MT5 Dev)" -ForegroundColor White
Write-Host "  ✅ Right-click context menu (folders)" -ForegroundColor White
Write-Host "  ✅ Quick launch PowerShell functions" -ForegroundColor White
Write-Host ""
Write-Host "🚀 How to use:" -ForegroundColor Yellow
Write-Host "  • Double-click desktop shortcut" -ForegroundColor White
Write-Host "  • Start Menu → Gemini CLI" -ForegroundColor White
Write-Host "  • Right-click any folder → Open Gemini CLI here" -ForegroundColor White
Write-Host "  • Run: .\start-web-interface.bat for web UI" -ForegroundColor White
Write-Host ""
Write-Host "⚡ PowerShell Profile Integration:" -ForegroundColor Yellow
Write-Host "  Add this to your PowerShell profile for instant access:" -ForegroundColor White
Write-Host "  . '$GeminiMT5Dir\gemini-profile.ps1'" -ForegroundColor Cyan
Write-Host ""
Write-Host "🗑️ To uninstall: .\install-system-integration.ps1 -Uninstall" -ForegroundColor Gray
