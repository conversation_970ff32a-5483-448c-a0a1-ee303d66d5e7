# PowerShell script to create Gemini CLI desktop shortcut
# Run this script as Administrator for best results

$DesktopPath = [Environment]::GetFolderPath("Desktop")
$ShortcutPath = "$DesktopPath\Gemini CLI - MT5 Dev.lnk"

# Create WScript Shell object
$WshShell = New-Object -comObject WScript.Shell

# Create shortcut
$Shortcut = $WshShell.CreateShortcut($ShortcutPath)

# Set shortcut properties
$Shortcut.TargetPath = "powershell.exe"
$Shortcut.Arguments = '-NoExit -Command "& {Write-Host \"🚀 Gemini CLI for MT5 Development\" -ForegroundColor Cyan; Write-Host \"Starting Gemini CLI...\" -ForegroundColor Green; cd \"$env:USERPROFILE\Documents\MT5_Projects\" -ErrorAction SilentlyContinue; if (-not (Test-Path \"$env:USERPROFILE\Documents\MT5_Projects\")) { New-Item -ItemType Directory -Path \"$env:USERPROFILE\Documents\MT5_Projects\" -Force | Out-Null; Write-Host \"Created MT5_Projects directory\" -ForegroundColor Yellow }; gemini}"'
$Shortcut.WorkingDirectory = "$env:USERPROFILE\Documents"
$Shortcut.Description = "Gemini CLI for MT5/MQL5 Development"
$Shortcut.IconLocation = "powershell.exe,0"

# Save shortcut
$Shortcut.Save()

Write-Host "✅ Desktop shortcut created: $ShortcutPath" -ForegroundColor Green
Write-Host "📁 Working directory: $env:USERPROFILE\Documents\MT5_Projects" -ForegroundColor Yellow
Write-Host "🎯 Double-click the shortcut to start Gemini CLI for MT5 development!" -ForegroundColor Cyan
