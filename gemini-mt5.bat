@echo off
title Gemini CLI - MT5 Development Assistant
color 0A

echo.
echo ========================================
echo    🚀 Gemini CLI - MT5 Development
echo ========================================
echo.

REM Create MT5 projects directory if it doesn't exist
if not exist "%USERPROFILE%\Documents\MT5_Projects" (
    mkdir "%USERPROFILE%\Documents\MT5_Projects"
    echo 📁 Created MT5_Projects directory
)

REM Try to find actual MT5 MQL5 directory
set "MT5_PATH="
for /d %%i in ("%APPDATA%\MetaQuotes\Terminal\*") do (
    if exist "%%i\MQL5" (
        set "MT5_PATH=%%i\MQL5"
        goto :found
    )
)

:found
if defined MT5_PATH (
    echo 📂 Found MT5 MQL5 directory: %MT5_PATH%
    cd /d "%MT5_PATH%"
) else (
    echo 📂 Using default MT5 projects directory
    cd /d "%USERPROFILE%\Documents\MT5_Projects"
)

echo.
echo 🎯 Current directory: %CD%
echo 💡 Type 'help' for MT5 development commands
echo 🔧 Starting Gemini CLI...
echo.

REM Start Gemini CLI
gemini

pause
