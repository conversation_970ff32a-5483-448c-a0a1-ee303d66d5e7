# 🚀 Gemini CLI Easy Access Setup Instructions

## ✅ **What's Ready to Use Right Now:**

All the files have been created and are ready to use! Here's how to set them up:

---

## 🖥️ **1. Desktop Shortcut (Easiest)**

**Method A: Run VBS Script**
```cmd
cscript //NoLogo create_shortcut.vbs
```

**Method B: Manual Creation**
1. Right-click on your desktop → New → Shortcut
2. Target: `powershell.exe -NoExit -Command "cd '$env:USERPROFILE\Documents\MT5_Projects'; if (-not (Test-Path '$env:USERPROFILE\Documents\MT5_Projects')) { New-Item -ItemType Directory -Path '$env:USERPROFILE\Documents\MT5_Projects' -Force }; Write-Host '🚀 Gemini CLI for MT5 Development' -ForegroundColor Cyan; gemini"`
3. Name: `Gemini CLI - MT5 Dev`
4. Click Finish

---

## ⚡ **2. Quick Access Scripts (Ready to Use)**

**Simple Launcher:**
```cmd
gemini-mt5.bat
```

**Advanced Launcher with Templates:**
```powershell
powershell -ExecutionPolicy Bypass -File "gemini-mt5-advanced.ps1"
```

**Show Templates:**
```powershell
powershell -ExecutionPolicy Bypass -File "gemini-mt5-advanced.ps1" -Templates
```

**Use Specific Template:**
```powershell
powershell -ExecutionPolicy Bypass -File "gemini-mt5-advanced.ps1" -Command scalping-ea
```

---

## 🌐 **3. Web Interface Setup**

**Step 1: Install Dependencies**
```cmd
copy web-interface-package.json package.json
npm install
```

**Step 2: Start Web Interface**
```cmd
start-web-interface.bat
```

**Or manually:**
```cmd
node gemini-web-server.js
```

**Access:** http://localhost:3000

---

## 🔧 **4. System Integration (Advanced)**

**Install Start Menu & Context Menu:**
```powershell
powershell -ExecutionPolicy Bypass -File "install-system-integration.ps1"
```

**Uninstall:**
```powershell
powershell -ExecutionPolicy Bypass -File "install-system-integration.ps1" -Uninstall
```

---

## 🎯 **Quick Test Commands**

**Test Gemini CLI:**
```cmd
gemini --version
```

**Test with Template:**
```cmd
gemini -p "Create a simple MT5 EA template with basic structure"
```

**Test Web Interface:**
1. Run: `start-web-interface.bat`
2. Open: http://localhost:3000
3. Click any template
4. Copy command and run in terminal

---

## 📋 **Available MT5 Templates**

1. **scalping-ea** - Scalping EA with RSI/MA signals
2. **trend-ea** - Trend following with EMA crossover
3. **custom-indicator** - Support/resistance indicator
4. **trade-manager** - Trade management script
5. **multi-symbol-ea** - Multi-currency EA
6. **grid-ea** - Grid trading system
7. **news-filter** - News-aware EA
8. **dashboard** - Account dashboard
9. **breakout-ea** - Breakout trading system
10. **martingale-ea** - Martingale system
11. **signal-copier** - Signal copying EA
12. **arbitrage-ea** - Arbitrage system

---

## 🚀 **Recommended Setup Order**

1. **Test Gemini CLI**: `gemini --version`
2. **Try Simple Script**: `gemini-mt5.bat`
3. **Test Templates**: `powershell -ExecutionPolicy Bypass -File "gemini-mt5-advanced.ps1" -Templates`
4. **Set up Web Interface**: `start-web-interface.bat`
5. **Create Desktop Shortcut**: `cscript //NoLogo create_shortcut.vbs`
6. **Install System Integration**: Run PowerShell script as admin

---

## 🐛 **Troubleshooting**

**Gemini CLI not found:**
- Check PATH includes: `C:\Users\<USER>\AppData\Roaming\npm`
- Restart terminal after PATH changes

**PowerShell execution policy:**
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

**Web interface won't start:**
- Install Node.js from https://nodejs.org/
- Run `npm install` in project directory

**Scripts won't run:**
- Right-click → Properties → Unblock (if downloaded)
- Run PowerShell as Administrator for system integration

---

## ✅ **Success Indicators**

- ✅ `gemini --version` shows version number
- ✅ `gemini-mt5.bat` opens Gemini CLI in MT5 directory
- ✅ Web interface opens at http://localhost:3000
- ✅ Desktop shortcut launches Gemini CLI
- ✅ Templates show MT5 development options

---

**🎉 You're all set for MT5 development with Gemini CLI!**
