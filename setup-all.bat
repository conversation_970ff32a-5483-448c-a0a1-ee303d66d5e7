@echo off
title Gemini CLI - Complete Setup
color 0E

echo.
echo ========================================
echo   🚀 Gemini CLI Complete Setup
echo ========================================
echo.
echo This script will set up all Gemini CLI access methods:
echo   1. Desktop shortcuts
echo   2. Start Menu integration  
echo   3. Context menu integration
echo   4. Web interface setup
echo   5. Quick access scripts
echo.

pause

echo.
echo 📋 Step 1: Creating desktop shortcut...
powershell -ExecutionPolicy Bypass -File "create_gemini_shortcut.ps1"
if %errorlevel% neq 0 (
    echo ❌ Failed to create desktop shortcut
) else (
    echo ✅ Desktop shortcut created
)

echo.
echo 🔧 Step 2: Installing system integration...
powershell -ExecutionPolicy Bypass -File "install-system-integration.ps1"
if %errorlevel% neq 0 (
    echo ❌ Failed to install system integration
) else (
    echo ✅ System integration installed
)

echo.
echo 🌐 Step 3: Setting up web interface...
if exist "web-interface-package.json" (
    copy "web-interface-package.json" "package.json" >nul 2>&1
    echo 📦 Installing web interface dependencies...
    npm install >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Failed to install web dependencies
        echo 💡 You can manually run: npm install
    ) else (
        echo ✅ Web interface dependencies installed
    )
) else (
    echo ⚠️ Web interface package.json not found
)

echo.
echo 🎉 Setup Complete!
echo.
echo ========================================
echo   📋 Available Access Methods
echo ========================================
echo.
echo 🖥️  Desktop Shortcut:
echo     Double-click "Gemini CLI - MT5 Dev" on desktop
echo.
echo 📱 Start Menu:
echo     Start Menu → Gemini CLI → Choose option
echo.
echo 🖱️  Context Menu:
echo     Right-click any folder → "Open Gemini CLI here"
echo.
echo 🌐 Web Interface:
echo     Run: start-web-interface.bat
echo     Or: http://localhost:3000
echo.
echo ⚡ Quick Scripts:
echo     gemini-mt5.bat - Simple launcher
echo     gemini-mt5-advanced.ps1 - Advanced launcher with templates
echo.
echo 💡 Tips:
echo   • All methods auto-detect your MT5 directory
echo   • Web interface provides templates and saved prompts
echo   • Use templates for common MT5 development tasks
echo   • Context menu works from any folder
echo.

pause
