# 🚀 Gemini CLI - Easy Access Methods for MT5 Development

This guide provides multiple convenient ways to access and use the Gemini CLI for MetaTrader 5 development.

## 📋 Quick Setup

**Run the complete setup:**
```batch
setup-all.bat
```

This will install all access methods automatically.

## 🎯 Access Methods

### 1. 🖥️ Desktop Shortcut

**What it does:** Double-click to instantly start Gemini CLI in MT5 development mode

**Setup:**
```powershell
powershell -ExecutionPolicy Bypass -File "create_gemini_shortcut.ps1"
```

**Usage:**
- Double-click "Gemini CLI - MT5 Dev" on your desktop
- Automatically navigates to your MT5 projects directory
- Starts Gemini CLI in interactive mode

### 2. 📱 Start Menu Integration

**What it does:** Adds Gemini CLI to your Windows Start Menu with multiple options

**Setup:**
```powershell
powershell -ExecutionPolicy Bypass -File "install-system-integration.ps1"
```

**Available shortcuts:**
- **Gemini CLI - Interactive**: Standard interactive mode
- **Gemini CLI - Web Interface**: Launches web-based interface
- **Gemini CLI - Templates**: Shows available MT5 templates

### 3. 🖱️ Context Menu Integration

**What it does:** Right-click any folder to open Gemini CLI in that location

**Features:**
- Right-click on any folder → "🚀 Open Gemini CLI here"
- Right-click in empty space → "🚀 Open Gemini CLI here"
- Automatically sets working directory to the selected folder

### 4. 🌐 Web Interface

**What it does:** Provides a user-friendly web interface for Gemini CLI

**Features:**
- Clean, modern web interface
- Pre-built MT5 development templates
- Save and manage frequently used prompts
- Copy commands to clipboard for terminal execution
- Responsive design for desktop and mobile

**Setup:**
```batch
start-web-interface.bat
```

**Access:** http://localhost:3000

### 5. ⚡ Quick Access Scripts

#### Simple Launcher (`gemini-mt5.bat`)
- Basic batch file launcher
- Auto-detects MT5 MQL5 directory
- Simple and reliable

#### Advanced Launcher (`gemini-mt5-advanced.ps1`)
- PowerShell script with advanced features
- Built-in MT5 development templates
- Command-line template execution
- Help system

**Usage examples:**
```powershell
# Interactive mode
.\gemini-mt5-advanced.ps1

# Show templates
.\gemini-mt5-advanced.ps1 -Templates

# Use specific template
.\gemini-mt5-advanced.ps1 -Command scalping-ea

# Show help
.\gemini-mt5-advanced.ps1 -Help
```

## 🔧 MT5 Development Templates

All access methods include these pre-built templates:

| Template | Description |
|----------|-------------|
| `scalping-ea` | Complete scalping EA with RSI/MA signals |
| `trend-ea` | Trend-following EA with EMA crossover |
| `custom-indicator` | Support/resistance indicator with alerts |
| `trade-manager` | Trade management script |
| `multi-symbol-ea` | Multi-currency pair EA |
| `grid-ea` | Grid trading system |
| `news-filter` | News-aware trading EA |
| `dashboard` | Account information dashboard |

## 🎯 Recommended Workflow

### For Beginners:
1. Use **Desktop Shortcut** for quick access
2. Try **Web Interface** for template browsing
3. Use **Context Menu** when working in specific folders

### For Advanced Users:
1. Use **Advanced PowerShell Script** with templates
2. Set up **PowerShell Profile** integration
3. Use **Context Menu** for project-specific work

### For Web Developers:
1. Use **Web Interface** as primary method
2. Customize templates in the web UI
3. Save frequently used prompts

## 🔄 PowerShell Profile Integration

Add instant access commands to your PowerShell profile:

```powershell
# Add to your PowerShell profile
. "C:\path\to\gemini-cli\gemini-profile.ps1"
```

**Available commands:**
- `gcli` - Start Gemini CLI
- `gweb` - Start Web Interface  
- `gtpl` - Show Templates

## 🗑️ Uninstallation

To remove system integration:
```powershell
powershell -ExecutionPolicy Bypass -File "install-system-integration.ps1" -Uninstall
```

## 🐛 Troubleshooting

### Gemini CLI not found
- Ensure Gemini CLI is installed: `npm install -g @google/gemini-cli`
- Check PATH includes: `C:\Users\<USER>\AppData\Roaming\npm`

### PowerShell execution policy
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### Web interface not starting
- Install Node.js from https://nodejs.org/
- Run: `npm install` in the project directory

### Context menu not appearing
- Run PowerShell as Administrator
- Re-run: `install-system-integration.ps1`

## 💡 Tips for MT5 Development

1. **Be Specific**: Include timeframes, symbols, and risk parameters in prompts
2. **Use Templates**: Start with templates and modify as needed
3. **Test Incrementally**: Build simple EAs first, then add complexity
4. **Save Prompts**: Use web interface to save frequently used requests
5. **Context Matters**: Run Gemini CLI from your MT5 project directory

## 🚀 Example Usage

```bash
# Quick EA creation
gemini -p "Create EURUSD scalping EA with 5-pip SL, RSI signals, 2% risk"

# Debug existing code
gemini -p "Debug this MQL5 EA and fix compilation errors: [paste code]"

# Custom indicator
gemini -p "Write support/resistance indicator with email alerts"
```

---

**Happy MT5 Development with Gemini CLI! 🎉**
