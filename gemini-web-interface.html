<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gemini CLI - MT5 Development Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .sidebar {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
        }
        
        .main-panel {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
        }
        
        .section-title {
            font-size: 1.4em;
            margin-bottom: 15px;
            color: #ffd700;
        }
        
        .template-grid {
            display: grid;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .template-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: #fff;
            padding: 12px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: left;
            font-size: 0.9em;
        }
        
        .template-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }
        
        .prompt-area {
            width: 100%;
            height: 150px;
            background: rgba(0,0,0,0.3);
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 10px;
            color: #fff;
            padding: 15px;
            font-size: 1em;
            resize: vertical;
            margin-bottom: 15px;
        }
        
        .prompt-area:focus {
            outline: none;
            border-color: #ffd700;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: #fff;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }
        
        .response-area {
            background: rgba(0,0,0,0.4);
            border-radius: 10px;
            padding: 20px;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-y: auto;
            max-height: 400px;
        }
        
        .saved-prompts {
            margin-top: 20px;
        }
        
        .saved-prompt-item {
            background: rgba(255,255,255,0.1);
            margin: 5px 0;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .saved-prompt-item:hover {
            background: rgba(255,255,255,0.2);
        }
        
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .status.loading {
            background: rgba(255, 193, 7, 0.3);
            color: #ffc107;
        }
        
        .status.success {
            background: rgba(40, 167, 69, 0.3);
            color: #28a745;
        }
        
        .status.error {
            background: rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Gemini CLI - MT5 Development Interface</h1>
            <p>AI-Powered MetaTrader 5 Development Assistant</p>
        </div>
        
        <div class="main-content">
            <div class="sidebar">
                <div class="section-title">📋 MT5 Templates</div>
                <div class="template-grid" id="templateGrid">
                    <!-- Templates will be populated by JavaScript -->
                </div>
                
                <div class="section-title">💾 Saved Prompts</div>
                <div class="saved-prompts" id="savedPrompts">
                    <div class="saved-prompt-item" onclick="loadPrompt('Create simple MT5 EA template')">
                        📝 Simple EA Template
                    </div>
                    <div class="saved-prompt-item" onclick="loadPrompt('Debug MT5 compilation errors')">
                        🐛 Debug Compilation Errors
                    </div>
                    <div class="saved-prompt-item" onclick="loadPrompt('Optimize MT5 EA performance')">
                        ⚡ Optimize EA Performance
                    </div>
                </div>
            </div>
            
            <div class="main-panel">
                <div class="section-title">💬 Prompt Input</div>
                <textarea class="prompt-area" id="promptInput" placeholder="Enter your MT5 development request here...

Examples:
• Create a scalping EA for EURUSD with RSI signals
• Debug this MQL5 code: [paste your code]
• Write a custom indicator for support/resistance levels
• Optimize this EA for better performance"></textarea>
                
                <div class="btn-group">
                    <button class="btn" onclick="executePrompt()">🚀 Execute with Gemini</button>
                    <button class="btn btn-secondary" onclick="savePrompt()">💾 Save Prompt</button>
                    <button class="btn btn-secondary" onclick="clearAll()">🗑️ Clear</button>
                </div>
                
                <div id="status"></div>
                
                <div class="section-title">📤 Response</div>
                <div class="response-area" id="responseArea">
                    Welcome to the Gemini CLI MT5 Development Interface!
                    
                    This web interface provides an easy way to interact with the Gemini CLI for MT5 development.
                    
                    🎯 How to use:
                    1. Select a template from the sidebar or write your own prompt
                    2. Click "Execute with Gemini" to run the command
                    3. View the response in this area
                    4. Save frequently used prompts for quick access
                    
                    Note: This interface simulates Gemini CLI commands. For actual execution, 
                    the commands will be copied to your clipboard to run in the terminal.
                </div>
            </div>
        </div>
    </div>

    <script>
        // MT5 Development Templates
        const mt5Templates = {
            'Scalping EA': 'Create a complete MT5 scalping Expert Advisor with 5-pip SL, 10-pip TP, using RSI and Moving Average signals on M1 timeframe. Include proper risk management (2% per trade), error handling, and input parameters.',
            'Trend Following EA': 'Write a trend-following MT5 EA using 20 EMA and 50 EMA crossover on H1 timeframe. Add MACD confirmation, trailing stop, and position sizing based on account balance.',
            'Custom Indicator': 'Create a custom MT5 indicator that shows support and resistance levels with alerts when price approaches these levels. Make it work on all timeframes with customizable colors.',
            'Trade Manager Script': 'Write an MT5 script for trade management that can: close all profitable trades, move stop loss to breakeven, and apply trailing stop to running positions.',
            'Multi-Symbol EA': 'Create an MT5 EA that trades multiple currency pairs (EURUSD, GBPUSD, USDJPY) using the same strategy but with different parameters for each pair.',
            'Grid Trading EA': 'Write a grid trading MT5 EA with configurable grid step, maximum trades, and recovery mechanism. Include proper money management and drawdown protection.',
            'News Filter EA': 'Create an MT5 EA that stops trading during high-impact news events. Include a news calendar integration and automatic trade closure before news.',
            'Dashboard Indicator': 'Write an MT5 dashboard indicator that shows account information, open trades, daily/weekly P&L, and market overview for major pairs.',
            'Breakout EA': 'Create a breakout trading EA that identifies key support/resistance levels and trades breakouts with proper confirmation signals.',
            'Martingale EA': 'Write a Martingale-based MT5 EA with configurable multiplier, maximum trades, and recovery mechanism. Include strict risk management.',
            'Signal Copier': 'Create an MT5 signal copying EA that can copy trades from signal providers with customizable lot sizing and risk management.',
            'Arbitrage EA': 'Write an arbitrage EA for MT5 that can identify and exploit price differences between correlated currency pairs.'
        };

        // Populate template grid
        function populateTemplates() {
            const grid = document.getElementById('templateGrid');
            Object.entries(mt5Templates).forEach(([name, prompt]) => {
                const btn = document.createElement('button');
                btn.className = 'template-btn';
                btn.textContent = name;
                btn.onclick = () => loadTemplate(prompt);
                grid.appendChild(btn);
            });
        }

        // Load template into prompt area
        function loadTemplate(prompt) {
            document.getElementById('promptInput').value = prompt;
        }

        // Load saved prompt
        function loadPrompt(prompt) {
            document.getElementById('promptInput').value = prompt;
        }

        // Execute prompt (simulate Gemini CLI execution)
        function executePrompt() {
            const prompt = document.getElementById('promptInput').value.trim();
            if (!prompt) {
                showStatus('Please enter a prompt first!', 'error');
                return;
            }

            const command = `gemini -p "${prompt.replace(/"/g, '\\"')}"`;
            
            showStatus('Preparing command for execution...', 'loading');
            
            // Copy command to clipboard
            navigator.clipboard.writeText(command).then(() => {
                showStatus('Command copied to clipboard! Paste and run in your terminal.', 'success');
                
                // Simulate response display
                const responseArea = document.getElementById('responseArea');
                responseArea.textContent = `Command ready to execute:
${command}

Instructions:
1. The command has been copied to your clipboard
2. Open your terminal/command prompt
3. Navigate to your MT5 project directory
4. Paste and run the command (Ctrl+V, then Enter)
5. The Gemini CLI will process your request and provide the MT5 code

Tip: You can also run the batch file or PowerShell script we created for easier access!`;
            }).catch(() => {
                showStatus('Could not copy to clipboard. Please copy the command manually.', 'error');
                const responseArea = document.getElementById('responseArea');
                responseArea.textContent = `Manual command to run:
${command}`;
            });
        }

        // Save prompt to local storage
        function savePrompt() {
            const prompt = document.getElementById('promptInput').value.trim();
            if (!prompt) {
                showStatus('Please enter a prompt to save!', 'error');
                return;
            }

            const savedPrompts = JSON.parse(localStorage.getItem('geminiMT5Prompts') || '[]');
            const promptName = prompt.substring(0, 50) + (prompt.length > 50 ? '...' : '');
            
            savedPrompts.push({ name: promptName, prompt: prompt, date: new Date().toISOString() });
            localStorage.setItem('geminiMT5Prompts', JSON.stringify(savedPrompts));
            
            showStatus('Prompt saved successfully!', 'success');
            loadSavedPrompts();
        }

        // Load saved prompts from local storage
        function loadSavedPrompts() {
            const savedPrompts = JSON.parse(localStorage.getItem('geminiMT5Prompts') || '[]');
            const container = document.getElementById('savedPrompts');
            
            // Clear existing saved prompts (keep default ones)
            const defaultPrompts = container.innerHTML;
            container.innerHTML = defaultPrompts;
            
            savedPrompts.forEach(item => {
                const div = document.createElement('div');
                div.className = 'saved-prompt-item';
                div.textContent = `📝 ${item.name}`;
                div.onclick = () => loadPrompt(item.prompt);
                container.appendChild(div);
            });
        }

        // Clear all inputs
        function clearAll() {
            document.getElementById('promptInput').value = '';
            document.getElementById('responseArea').textContent = 'Cleared. Ready for new input.';
            showStatus('Interface cleared.', 'success');
        }

        // Show status message
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            
            if (type !== 'loading') {
                setTimeout(() => {
                    statusDiv.textContent = '';
                    statusDiv.className = 'status';
                }, 3000);
            }
        }

        // Initialize the interface
        document.addEventListener('DOMContentLoaded', () => {
            populateTemplates();
            loadSavedPrompts();
        });
    </script>
</body>
</html>
