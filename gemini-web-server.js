const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = 3000;

// Middleware
app.use(express.json());
app.use(express.static('.'));

// Serve the main HTML file
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'gemini-web-interface.html'));
});

// Execute Gemini CLI command
app.post('/execute', (req, res) => {
    const { prompt } = req.body;
    
    if (!prompt) {
        return res.status(400).json({ error: 'Prompt is required' });
    }

    console.log(`Executing Gemini CLI with prompt: ${prompt.substring(0, 100)}...`);

    // Spawn Gemini CLI process
    const gemini = spawn('gemini', ['-p', prompt], {
        shell: true,
        stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    gemini.stdout.on('data', (data) => {
        output += data.toString();
    });

    gemini.stderr.on('data', (data) => {
        errorOutput += data.toString();
    });

    gemini.on('close', (code) => {
        if (code === 0) {
            res.json({ 
                success: true, 
                output: output,
                command: `gemini -p "${prompt}"`
            });
        } else {
            res.json({ 
                success: false, 
                error: errorOutput || 'Command failed',
                output: output,
                code: code
            });
        }
    });

    gemini.on('error', (error) => {
        console.error('Error executing Gemini CLI:', error);
        res.json({ 
            success: false, 
            error: `Failed to execute Gemini CLI: ${error.message}`,
            suggestion: 'Make sure Gemini CLI is installed and accessible in PATH'
        });
    });

    // Set timeout for long-running commands
    setTimeout(() => {
        gemini.kill();
        res.json({ 
            success: false, 
            error: 'Command timed out after 60 seconds',
            output: output
        });
    }, 60000);
});

// Get system info
app.get('/system-info', (req, res) => {
    const info = {
        platform: process.platform,
        nodeVersion: process.version,
        workingDirectory: process.cwd(),
        timestamp: new Date().toISOString()
    };
    
    // Check if Gemini CLI is accessible
    const geminiCheck = spawn('gemini', ['--version'], { shell: true });
    
    geminiCheck.on('close', (code) => {
        info.geminiCliAvailable = code === 0;
        res.json(info);
    });
    
    geminiCheck.on('error', () => {
        info.geminiCliAvailable = false;
        res.json(info);
    });
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Gemini CLI Web Interface running at http://localhost:${PORT}`);
    console.log(`📁 Serving from: ${__dirname}`);
    console.log(`🔧 Make sure Gemini CLI is installed and accessible`);
    
    // Try to open browser automatically
    const open = require('child_process').spawn('cmd', ['/c', 'start', `http://localhost:${PORT}`], {
        stdio: 'ignore',
        detached: true
    });
    open.unref();
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down Gemini CLI Web Interface...');
    process.exit(0);
});
