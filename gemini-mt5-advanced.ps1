# Advanced PowerShell script for Gemini CLI with MT5 development features
param(
    [string]$Command = "",
    [switch]$Templates,
    [switch]$Help
)

# Set console colors and title
$Host.UI.RawUI.WindowTitle = "Gemini CLI - MT5 Development Assistant"
$Host.UI.RawUI.BackgroundColor = "DarkBlue"
$Host.UI.RawUI.ForegroundColor = "White"
Clear-Host

# Display header
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    🚀 Gemini CLI - MT5 Development    " -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# MT5 Development Templates
$MT5Templates = @{
    "scalping-ea" = "Create a complete MT5 scalping Expert Advisor with 5-pip SL, 10-pip TP, using RSI and Moving Average signals on M1 timeframe. Include proper risk management (2% per trade), error handling, and input parameters."
    
    "trend-ea" = "Write a trend-following MT5 EA using 20 EMA and 50 EMA crossover on H1 timeframe. Add MACD confirmation, trailing stop, and position sizing based on account balance."
    
    "custom-indicator" = "Create a custom MT5 indicator that shows support and resistance levels with alerts when price approaches these levels. Make it work on all timeframes with customizable colors."
    
    "trade-manager" = "Write an MT5 script for trade management that can: close all profitable trades, move stop loss to breakeven, and apply trailing stop to running positions."
    
    "multi-symbol-ea" = "Create an MT5 EA that trades multiple currency pairs (EURUSD, GBPUSD, USDJPY) using the same strategy but with different parameters for each pair."
    
    "grid-ea" = "Write a grid trading MT5 EA with configurable grid step, maximum trades, and recovery mechanism. Include proper money management and drawdown protection."
    
    "news-filter" = "Create an MT5 EA that stops trading during high-impact news events. Include a news calendar integration and automatic trade closure before news."
    
    "dashboard" = "Write an MT5 dashboard indicator that shows account information, open trades, daily/weekly P&L, and market overview for major pairs."
}

# Function to display available templates
function Show-Templates {
    Write-Host "📋 Available MT5 Development Templates:" -ForegroundColor Green
    Write-Host ""
    foreach ($key in $MT5Templates.Keys) {
        Write-Host "  • $key" -ForegroundColor Yellow
        Write-Host "    $($MT5Templates[$key].Substring(0, [Math]::Min(80, $MT5Templates[$key].Length)))..." -ForegroundColor Gray
        Write-Host ""
    }
    Write-Host "Usage: .\gemini-mt5-advanced.ps1 -Command template-name" -ForegroundColor Cyan
}

# Function to display help
function Show-Help {
    Write-Host "🔧 Gemini CLI MT5 Development Helper" -ForegroundColor Green
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\gemini-mt5-advanced.ps1                    # Start interactive mode"
    Write-Host "  .\gemini-mt5-advanced.ps1 -Templates         # Show available templates"
    Write-Host "  .\gemini-mt5-advanced.ps1 -Command <template># Use specific template"
    Write-Host "  .\gemini-mt5-advanced.ps1 -Help             # Show this help"
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\gemini-mt5-advanced.ps1 -Command scalping-ea"
    Write-Host "  .\gemini-mt5-advanced.ps1 -Command custom-indicator"
    Write-Host ""
}

# Setup working directory
$MT5ProjectsPath = "$env:USERPROFILE\Documents\MT5_Projects"
if (-not (Test-Path $MT5ProjectsPath)) {
    New-Item -ItemType Directory -Path $MT5ProjectsPath -Force | Out-Null
    Write-Host "📁 Created MT5_Projects directory: $MT5ProjectsPath" -ForegroundColor Yellow
}

# Try to find actual MT5 MQL5 directory
$MT5Path = $null
$MetaQuotesPath = "$env:APPDATA\MetaQuotes\Terminal"
if (Test-Path $MetaQuotesPath) {
    $TerminalDirs = Get-ChildItem -Path $MetaQuotesPath -Directory
    foreach ($dir in $TerminalDirs) {
        $MQL5Path = Join-Path $dir.FullName "MQL5"
        if (Test-Path $MQL5Path) {
            $MT5Path = $MQL5Path
            break
        }
    }
}

if ($MT5Path) {
    Write-Host "📂 Found MT5 MQL5 directory: $MT5Path" -ForegroundColor Green
    Set-Location $MT5Path
} else {
    Write-Host "📂 Using MT5 projects directory: $MT5ProjectsPath" -ForegroundColor Yellow
    Set-Location $MT5ProjectsPath
}

Write-Host "🎯 Current directory: $(Get-Location)" -ForegroundColor Cyan
Write-Host ""

# Handle command line arguments
if ($Help) {
    Show-Help
    return
}

if ($Templates) {
    Show-Templates
    return
}

if ($Command -and $MT5Templates.ContainsKey($Command)) {
    Write-Host "🚀 Executing template: $Command" -ForegroundColor Green
    Write-Host ""
    $prompt = $MT5Templates[$Command]
    Write-Host "Prompt: $prompt" -ForegroundColor Gray
    Write-Host ""
    & gemini -p $prompt
    return
}

if ($Command -and -not $MT5Templates.ContainsKey($Command)) {
    Write-Host "❌ Template '$Command' not found. Use -Templates to see available options." -ForegroundColor Red
    return
}

# Interactive mode
Write-Host "💡 Quick Commands:" -ForegroundColor Yellow
Write-Host "  Type 'templates' to see MT5 development templates" -ForegroundColor Gray
Write-Host "  Type 'help' for more information" -ForegroundColor Gray
Write-Host ""
Write-Host "🔧 Starting Gemini CLI in interactive mode..." -ForegroundColor Green
Write-Host ""

# Start Gemini CLI
& gemini
