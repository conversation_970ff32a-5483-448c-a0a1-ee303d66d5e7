@echo off
title Gemini CLI Web Interface
color 0B

echo.
echo ========================================
echo   🌐 Gemini CLI Web Interface Setup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is available
echo.

REM Check if package.json exists for web interface
if not exist "web-interface-package.json" (
    echo ❌ Web interface package.json not found
    echo Please make sure all files are in the same directory
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    copy "web-interface-package.json" "package.json" >nul
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
    echo ✅ Dependencies installed successfully
    echo.
)

echo 🚀 Starting Gemini CLI Web Interface...
echo 🌐 Opening http://localhost:3000 in your browser...
echo.
echo 💡 Tips:
echo   - Use Ctrl+C to stop the server
echo   - The interface will auto-open in your browser
echo   - Make sure Gemini CLI is installed and in PATH
echo.

REM Start the web server
node gemini-web-server.js

pause
