Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get desktop path
DesktopPath = WshShell.SpecialFolders("Desktop")
ShortcutPath = DesktopPath & "\Gemini CLI - MT5 Dev.lnk"

' Create MT5 Projects directory
MT5ProjectsPath = WshShell.ExpandEnvironmentStrings("%USERPROFILE%") & "\Documents\MT5_Projects"
If Not fso.FolderExists(MT5ProjectsPath) Then
    fso.CreateFolder(MT5ProjectsPath)
End If

' Create shortcut
Set Shortcut = WshShell.CreateShortcut(ShortcutPath)
Shortcut.TargetPath = "powershell.exe"
Shortcut.Arguments = "-NoExit -Command ""cd '" & MT5ProjectsPath & "'; Write-Host '🚀 Gemini CLI for MT5 Development' -ForegroundColor Cyan; Write-Host 'Current directory:' (Get-Location) -ForegroundColor Yellow; gemini"""
Shortcut.WorkingDirectory = MT5ProjectsPath
Shortcut.Description = "Gemini CLI for MT5/MQL5 Development"
Shortcut.IconLocation = "powershell.exe,0"
Shortcut.Save

WScript.Echo "✅ Desktop shortcut created successfully!"
WScript.Echo "📁 MT5 Projects directory: " & MT5ProjectsPath
WScript.Echo "🖥️ Shortcut location: " & ShortcutPath
